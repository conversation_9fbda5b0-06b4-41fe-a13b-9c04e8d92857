# frozen_string_literal: true

require 'functions_framework'
require 'google/cloud/pubsub'
require 'json'

require_relative 'functions/process_feedback'

FunctionsFramework.logger.level = ENV.fetch('LOGGER_LEVEL', 'INFO').to_sym

FunctionsFramework.on_startup do
  pubsub = Google::Cloud::Pubsub.new
  topic = pubsub.topic('gemini-on-feedback-processed')

  set_global :topic, topic
end

FunctionsFramework.cloud_event 'process_feedback' do |event|
  event_data = JSON.parse(Base64.decode64(event.data['message']['data']), symbolize_names: true)
  response = Functions::ProcessFeedback.call(event_data)

  global(:topic).publish(response.to_json)

  logger.info "Feedback processed - id: #{response[:processed_feedback][:feedback_id]}"

  true
end
