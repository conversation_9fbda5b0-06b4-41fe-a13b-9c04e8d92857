# frozen_string_literal: true

libs = Dir["#{File.dirname(__FILE__)}/../lib/**/*.rb"]
libs.each { |lib| require lib }

module Functions
  # Function responsible for processing feedbacks
  #
  class ProcessFeedback
    SERVICE_TYPES = {
      cleaning: ::FeedbackRules::Cleaning
    }.freeze

    def self.call(data)
      review = data[:feedback][:review]
      optionals = data[:job][:optionals]

      service_type = SERVICE_TYPES[data[:feedback][:service].to_sym]&.call(optionals:)
      return unless service_type

      prompt = "Aplique neste feedback: #{review}, estas regras: #{service_type.rules}"

      result = Request.call(prompt, opts: service_type)
      return result if result['error']

      format_feedback(data, result)
    end

    def self.format_feedback(data, result)
      format_feedback = data.merge(processed_feedback: data[:feedback]).except(:feedback, :job)
      format_feedback[:processed_feedback][:feedback_id] = format_feedback[:processed_feedback].delete(:id)
      format_feedback[:processed_feedback][:review] = result.split.join(' ')

      format_feedback
    end
  end
end
