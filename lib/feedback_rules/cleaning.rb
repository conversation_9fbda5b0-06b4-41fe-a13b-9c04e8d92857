# frozen_string_literal: true

module FeedbackRules
  # Class responsible for defining cleaning rules
  #
  class Cleaning < Base
    def initialize(optionals:)
      @optionals = optionals
      @service = 'cleaning'
      @service_optionals = fetch_service_optionals
      @service_to_do_list = fetch_service_to_do_list
      @tasks_does_not_do = fetch_tasks_does_not_do
      super
    end

    private

    def fetch_service_optionals
      <<~PROMPT
        #{@optionals}
      PROMPT
    end

    def fetch_service_to_do_list
      <<~PROMPT
        * Limpar o chão e todas as superfícies acessíveis (varrer, aspirar e/ou passar pano);
        * Limpar a parte externa dos móveis e eletrodomésticos;
        * Arrastar e limpar embaixo de móveis e eletrodomésticos leves;
        * Limpar pia e bancadas;
        * <PERSON>var e guardar as louças;
        * Lavar e higienizar vaso sanitário, box, pia e banheira;
        * Limpar espelhos em geral;
        * Arrumar camas e dormitórios em geral;
        * Retirar e substituir todos os sacos de lixo;
        * Organizar os materiais e produtos utilizados na limpeza;
        * Lavar todos os panos usados.

        O cliente pode contratar as seguintes tarefas opcionais:

        * Limpar interior das janelas;
        * Limpar e organizar interior dos armários da cozinha;
        * Limpar e organizar interior da geladeira;
        * Aspirar tapetes e estofados;
        * Limpar área externa (quintais e sacadas);
        * 2 horas de passadoria;
        * Lavar e estender roupas;
        * Levar produtos de limpeza.

        Neste caso ele contratou somente:

        #{@service_optionals}

        Não gere uma sugestão de melhoria baseado em um opcional que o cliente não contratou.
      PROMPT
    end

    def fetch_tasks_does_not_do
      <<~PROMPT
        * Limpar teto ou superfícies fora do alcance;
        * Limpar lustres ou ventiladores de teto;
        * Movimentar itens muito pesados;
        * Limpeza da área externa de janelas;
        * Limpeza interna de piscina, churrasqueiras, decks, telhados e calhas;
        * Serviços de jardinagem;
        * Limpar animais de estimação ou a sujeira gerada por esses;
        * Passear com animais de estimação;
        * Lavagem de tapetes e estofados grandes;
        * Lavagem de ternos, roupas de festa, e outros peças que necessitem de especialistas (mesmo com opcional de LAVAR ROUPAS contratado);
        * Retirada de mofo de paredes e teto;
        * Utilização de produtos abrasivos como ácidos e outros;
        * Remover marcas de tintas sem o fornecimento de produtos específicos;
        * Retirada de materiais de construção e quaisquer outros resíduos de obras em grande volume que não possam ser deixados na área comum de descarte do condomínio.
      PROMPT
    end
  end
end
