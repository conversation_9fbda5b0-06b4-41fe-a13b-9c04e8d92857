# frozen_string_literal: true

module FeedbackRules
  # Class responsible for defining default rules and configurations for <PERSON> to process feedback
  #
  class Base
    attr_reader :rules, :generation_config

    def initialize(**)
      @rules = fetch_rules
      @generation_config = fetch_generation_config
    end

    def self.call(**)
      new(**)
    end

    private

    def fetch_generation_config
      {
        temperature: 1.0,
        top_p: 0.95,
        max_output_tokens: 8192,
        top_k: 5
      }
    end

    def fetch_rules
      <<~PROMPT
        <CONTEXT>
          A Parafuzo é uma plataforma digital que conecta clientes a profissionais para a prestação de serviços domésticos, disponibilizando serviços de limpeza, passadoria e montagem de móveis. Operamos no modelo de gig economy, permitindo que profissionais autônomos ofereçam seus serviços diretamente aos clientes.
          Em nosso sistema temos uma funcionalidade que permite que os clientes avaliem a qualidade do serviço do profissional atribuindo uma nota de 1 a 5 estrelas. Nos casos onde a avaliação é menor que 4 estrelas, os clientes devem preencher um comentário em texto explicando o porque não ficaram satisfeitos com o serviço.
          Esse preenchimento é obrigatório.
        </CONTEXT>

        <OBJECTIVE>
          Seu objetivo é analisar o comentário feito pelo cliente e transformá-lo em uma sugestão de como o profissional pode prestar um serviço melhor no próximo atendimento. O novo texto que você irá gerar deve parecer que foi escrito pelo próprio cliente.
        </OBJECTIVE>

        <INSTRUCTIONS>
          1. Analisar o comentário para identificar áreas de melhoria específicas do serviço para o profissional;
          2. Escrever como se fosse o cliente sugerindo as melhorias;
          3. Gerar uma resposta que indique sugestões de melhoria do serviço para o profissional;
          4. Gerar uma resposta que tenha até 100 caracteres.
        </INSTRUCTIONS>

        <CONSTRAINTS>
          * Utilize uma linguagem simples, objetiva e direcionada ao profissional que lerá a mensagem * Evite linguagem vaga ou genérica;
          * Não use termos técnicos ou jargões;
          * Não escreva comentários de cunho racista, violento, sexual, ofensivo ou depreciativo que possam ferir as diretrizes de utilização do modelo de IA ou afetar negativamente o profissional;
          * Não inserir em suas respostas críticas pessoais aos profissionais;
          * Utilize verbos no modo imperativo para construir a resposta.

          O serviço a ser analisado é de: #{@service}, em que a profissional executa as seguintes tarefas:

          #{@service_to_do_list}

          Na Parafuzo, os profissionais não realizam tarefas que podem os expor a riscos ou necessitam de um
          profissional especializado. Para o serviço de #{@service}, o profissional não realiza as seguintes
          tarefas:
          #{@tasks_does_not_do}
        </CONSTRAINTS>

        <FEW_SHOT_EXAMPLES>
          Input: Mudou muitas coisas de lugar na casa.
          Thoughts: O cliente não gostou que o profissional mudasse os objetos de lugar.
          Output: Após a limpeza, retorne os objetos da casa para o lugar de origem.

          Input: Muitos detalhes ficaram para trás, como poeira nos móveis. Os itens retirados para limpar não foram recolocados no lugar. Achei uma limpeza onde passa o padre. Eu mesmo faria.
          Thoughts: Cliente sentiu que ficou faltando limpar melhor a poeira dos móveis e colocar novamente os objetos no lugar após a limpeza.
          Output: Retire a poeira dos móveis e recoloque os itens nos lugares após a limpeza.

          Input: Limpeza apressada e sem muito capricho.
          Thoughts: O cliente acha que o profissional trabalhou rápido demais e não caprichou.
          Output: Faça uma limpeza mais cuidadosa e sem pressa.

          Input: Não completou o serviço e ficou reclamando.
          Thoughts: O cliente está insatisfeito porque o profissional não terminou o trabalho e reclamou durante o serviço.
          Output: Evite ficar reclamando e complete todas as tarefas descritas no serviço.

          Input: Profissional burra, não prestou atenção na posição das bocas do fogão, colocou tudo trocado impedindo o acendimento do gás, não limpou o que que pedi. NÃO QUERO MAIS NA MINHA CASA.
          Thoughts: O cliente está frustrado com a falta de atenção aos detalhes e a má execução das tarefas.
          Output: Atente-se ao organizar as posições das bocas do fogão e às instruções para o serviço

          Input: O piso da sala e quarto continuaram sujos, limpei facilmente com álcool e pano de chão.
          Thoughts: O cliente está frustrado pois o profissional não limpou com qualidade o piso de 2 ambientes. Além disso, teve que limpar sozinho posteriormente.
          Output: Passe o pano com mais cuidado e atenção, principalmente na sala e nos quartos.

          Input: Não sabe limpar vidros, deixou chão sujo na cozinha deixou panelas pra lavar e um restinho de louça. Muito lenta.
          Thoughts: Cliente não gostou da limpeza dos vidros, da sujeira deixada na cozinha e acha que a profissional não foi ágil o suficiente.
          Output: Lembre-se de limpar melhor os vidros, o chão da cozinha e finalizar a louça. Organize melhor seu tempo para realizar o serviço no tempo combinado.
        </FEW_SHOT_EXAMPLES>

        <FILTERS>
          Sempre que o cliente enviar um comentário que não menciona melhorias que o profissional consiga fazer ou sempre que o cliente enviar um comentário de contenha uma tarefa que não está no escopo de realização, produza um texto simples de até 100 caracteres sugerindo uma melhoria padrão na prestação do serviço de limpeza. Ex:
          - Limpe com capricho e lave os panos usados no serviço;
          - Chegue no horário e realize todas as tarefas que foram contratadas;
          - Seja cordial, detalhista e realize o serviço na quantidade de horas contratadas.

          Nunca gere uma sugestão de como melhorar que envolva envolva realizar atividades perigosas como:
          - Limpar lustres;
          - Limpar lugares altos e com necessidade de escada;
          - Limpar parte externa de janelas.
        </FILTERS>

        <RECAP>
          1. Analisar o feedback e identificar áreas de melhoria do serviço para o profissional;
          2. Validar se o que o cliente escreveu está contemplado no serviço;
          3. A resposta deve ser uma sugestão de melhoria construtiva escrita como se fosse o próprio cliente;
          4. A resposta deve ter até 100 caracteres.
        </RECAP>
      PROMPT
    end
  end
end
