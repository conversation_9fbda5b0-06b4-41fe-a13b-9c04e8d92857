# frozen_string_literal: true

require 'net/http'
require 'json'

# Class responsible for requests to the Gemini API
#
class Request
  GEMINI_URI = URI('https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent')
  GEMINI_URI.query = URI.encode_www_form(key: ENV.fetch('GEMINI_API_KEY', 'missing_env'))

  def self.call(prompt, opts:)
    req = Net::HTTP::Post.new(GEMINI_URI)
    req['Content-Type'] = 'application/json'

    req.body = build_body(prompt, opts)
    res = Net::HTTP.start(GEMINI_URI.hostname, GEMINI_URI.port, use_ssl: true) { |http| http.request(req) }

    build_response(res)
  end

  def self.build_body(prompt, opts)
    {
      contents: [{ parts: [{ text: prompt }] }],
      generationConfig: opts.generation_config
    }.to_json
  end

  def self.build_response(res)
    response_json = JSON.parse(res.body)

    return response_json if response_json['error']

    response_json['candidates'].first['content']['parts'].first['text']
  end
end
