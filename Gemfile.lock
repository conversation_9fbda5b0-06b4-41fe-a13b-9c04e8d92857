GEM
  remote: https://rubygems.org/
  specs:
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ast (2.4.3)
    base64 (0.3.0)
    bigdecimal (3.2.2)
    cloud_events (0.7.1)
    concurrent-ruby (1.3.5)
    date (3.4.1)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    diff-lcs (1.6.2)
    docile (1.4.0)
    faraday (2.13.1)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    faraday-retry (2.3.1)
      faraday (~> 2.0)
    functions_framework (1.6.0)
      cloud_events (>= 0.7.0, < 2.a)
      puma (>= 4.3.0, < 7.a)
      rack (>= 2.1, < 4.a)
    gapic-common (1.0.0)
      faraday (>= 1.9, < 3.a)
      faraday-retry (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      google-protobuf (>= 3.25, < 5.a)
      googleapis-common-protos (~> 1.6)
      googleapis-common-protos-types (~> 1.15)
      googleauth (~> 1.12)
      grpc (~> 1.66)
    google-cloud-core (1.8.0)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-env (2.3.1)
      base64 (~> 0.2)
      faraday (>= 1.0, < 3.a)
    google-cloud-errors (1.5.0)
    google-cloud-pubsub (2.22.0)
      concurrent-ruby (~> 1.1)
      google-cloud-core (~> 1.5)
      google-cloud-pubsub-v1 (~> 1.7)
      retriable (~> 3.1)
    google-cloud-pubsub-v1 (1.9.0)
      gapic-common (~> 1.0)
      google-cloud-errors (~> 1.0)
      google-iam-v1 (~> 1.3)
    google-iam-v1 (1.4.0)
      gapic-common (~> 1.0)
      google-cloud-errors (~> 1.0)
      grpc-google-iam-v1 (~> 1.11)
    google-logging-utils (0.2.0)
    google-protobuf (4.31.1)
      bigdecimal
      rake (>= 13)
    googleapis-common-protos (1.7.0)
      google-protobuf (>= 3.18, < 5.a)
      googleapis-common-protos-types (~> 1.7)
      grpc (~> 1.41)
    googleapis-common-protos-types (1.20.0)
      google-protobuf (>= 3.18, < 5.a)
    googleauth (1.14.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    grpc (1.72.0)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc-google-iam-v1 (1.11.0)
      google-protobuf (>= 3.18, < 5.a)
      googleapis-common-protos (~> 1.7.0)
      grpc (~> 1.41)
    io-console (0.8.0)
    irb (1.14.3)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.12.2)
    jwt (2.10.1)
      base64
    language_server-protocol (3.17.0.5)
    lint_roller (1.1.0)
    logger (1.7.0)
    multi_json (1.15.0)
    net-http (0.6.0)
      uri
    nio4r (2.7.4)
    os (1.1.4)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    prism (1.4.0)
    psych (5.2.2)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (3.1.12)
    rainbow (3.1.1)
    rake (13.3.0)
    rdoc (6.10.0)
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.6.0)
      io-console (~> 0.5)
    retriable (3.1.2)
    rspec (3.13.1)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.4)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.4)
    rubocop (1.76.0)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.45.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= *******)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rspec (3.6.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    ruby-progressbar (1.13.0)
    signet (0.20.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    stringio (3.1.2)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)

PLATFORMS
  ruby

DEPENDENCIES
  debug (~> 1.10)
  functions_framework (~> 1.6)
  google-cloud-pubsub (~> 2.22)
  google-protobuf (~> 4.31)
  grpc (~> 1.72)
  rspec (~> 3.13)
  rubocop (~> 1.76)
  rubocop-performance (~> 1.25)
  rubocop-rspec (~> 3.6)
  simplecov (~> 0.22)
  simplecov_json_formatter (~> 0.1.4)

RUBY VERSION
   ruby 3.2.4p170

BUNDLED WITH
   2.5.6
