# frozen_string_literal: true

require 'spec_helper'
require 'google/cloud/pubsub'

require_relative '../lib/request'

RSpec.describe 'FunctionsFramework', type: :function do
  let(:topic) { instance_double Google::Cloud::PubSub::Topic, publish: true }
  let(:pubsub) { instance_double Google::Cloud::PubSub::Project, topic: }

  before do
    allow(Google::Cloud::Pubsub).to receive(:new).and_return(pubsub)
    allow(FunctionsFramework.logger).to receive(:info)

    allow(Request).to receive(:call).and_return('Evite mudar os móveis sem consultar o cliente.')
  end

  describe '.on_startup' do
    it {
      load_temporary 'app.rb' do
        globals = run_startup_tasks 'process_feedback'

        expect(globals[:topic]).to eq(topic)
      end
    }
  end

  describe '.process_feedback' do
    let(:data) do
      {
        feedback: {
          id: 'feedback_id',
          tasker_id: 'tasker_id',
          review: 'Mudou muitas coisas de lugar na casa',
          service: 'cleaning'
        },
        job: {
          optionals: ['Limpar interior das janelas, Limpar e organizar interior da geladeira']
        }
      }
    end

    let(:event) do
      make_cloud_event payload,
                       source: '//pubsub.googleapis.com/projects/sample-project/topics/gcf-test',
                       type: 'google.cloud.pubsub.topic.v1.messagePublished'
    end

    let(:payload) do
      { '@type' => 'type.googleapis.com/google.pubsub.v1.PubsubMessage',
        'message' => { 'data' => Base64.encode64(data.to_json) } }
    end

    it 'returns true' do
      load_temporary 'app.rb' do
        call_event 'process_feedback', event, globals: { topic: }

        expect(true).to be_truthy
      end
    end

    it 'publishes to pubsub topic' do
      expected_result = { processed_feedback: { tasker_id: 'tasker_id',
                                                review: 'Evite mudar os móveis sem consultar o cliente.',
                                                service: 'cleaning', feedback_id: 'feedback_id' } }.to_json

      load_temporary 'app.rb' do
        call_event 'process_feedback', event, globals: { topic: }

        expect(topic).to have_received(:publish).with(expected_result)
      end
    end

    it 'logs successfull insert' do
      load_temporary 'app.rb' do
        call_event 'process_feedback', event, globals: { topic: }

        expect(FunctionsFramework.logger).to have_received(:info)
          .with("Feedback processed - id: #{data[:feedback][:id]}")
      end
    end
  end
end
