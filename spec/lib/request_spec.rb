# frozen_string_literal: true

libs = Dir["#{File.dirname(__FILE__)}/../../lib/**/*.rb"]
libs.each { |lib| require lib }

RSpec.describe Request do
  describe '.call' do
    before do
      content = {
        candidates: [{ content: { parts: [{ text: 'Gostaria que meus objetos fossem mantidos em seus lugares.' }] } }]
      }.to_json

      req_instance = instance_double(Net::HTTP::Post)

      allow(req_instance).to receive(:body).and_return(content)
      allow(Net::HTTP).to receive(:start).and_return(req_instance)
    end

    context 'when request success' do
      let(:gemini_feedback_config) do
        FeedbackRules::Cleaning.call(optionals: ['Limpar interior das janelas'])
      end

      let(:prompt) do
        "Aplique neste feedback: 'Mudou muitas coisas de lugar na casa',
         estas regras: #{gemini_feedback_config.rules}"
      end

      it 'returns processed feedback' do
        expect(described_class.call(prompt, opts: gemini_feedback_config))
          .to eq('Gostaria que meus objetos fossem mantidos em seus lugares.')
      end
    end

    context 'when request failure' do
      before do
        req_instance = instance_double(Net::HTTP::Post)

        allow(req_instance).to receive(:body).and_return(content)
        allow(Net::HTTP).to receive(:start).and_return(req_instance)
      end

      let(:content) do
        {
          error: {
            code: 400,
            message: 'API key not valid. Please pass a valid API key.',
            status: 'INVALID_ARGUMENT'
          }
        }.to_json
      end

      let(:gemini_feedback_config) do
        FeedbackRules::Cleaning.call(optionals: ['Limpar interior das janelas'])
      end

      let(:prompt) do
        "Aplique neste feedback: 'Mudou muitas coisas de lugar na casa',
         estas regras: #{gemini_feedback_config.rules}"
      end

      it 'returns error' do
        expect(described_class.call(prompt, opts: gemini_feedback_config))
          .to eq({
                   'error' => { 'code' => 400,
                                'message' => 'API key not valid. Please pass a valid API key.',
                                'status' => 'INVALID_ARGUMENT' }
                 })
      end
    end
  end
end
