# frozen_string_literal: true

require_relative '../../functions/process_feedback'

RSpec.describe Functions::ProcessFeedback do
  describe '.call' do
    context 'when service type is cleaning' do
      before do
        allow(Request).to receive(:call).and_return('Evite mudar os móveis sem consultar o cliente.')
        allow(FeedbackRules::Cleaning).to receive(:call)
      end

      let(:data) do
        {
          feedback: {
            id: 'feedback_id',
            tasker_id: 'tasker_id',
            review: 'Mudou muitas coisas de lugar na casa',
            service: 'cleaning'
          },
          job: {
            optionals: ['Limpar interior das janelas, Limpar e organizar interior da geladeira']
          }
        }
      end

      it 'returns feedback processed to cleaning service' do
        described_class.call(data)

        expect(FeedbackRules::Cleaning).to have_received(:call)
      end
    end

    context 'when text has line breaks' do
      before do
        allow(Request).to receive(:call).and_return("Evite \n mudar os móveis de lugar. \n")
      end

      let(:data) do
        {
          feedback: {
            id: 'feedback_id',
            tasker_id: 'tasker_id',
            review: 'Mudou muitas coisas de lugar na casa',
            service: 'cleaning'
          },
          job: {
            optionals: ['Limpar interior das janelas, Limpar e organizar interior da geladeira']
          }
        }
      end

      it 'removes line breaks' do
        described_class.call(data)

        expect(described_class.call(data)[:processed_feedback][:review]).to eq('Evite mudar os móveis de lugar.')
      end
    end

    context 'when service type not exists' do
      let(:data) do
        {
          feedback: {
            id: 'feedback_id',
            tasker_id: 'tasker_id',
            review: 'Mudou muitas coisas de lugar na casa',
            service: 'abacate'
          },
          job: {
            optionals: ['Limpar e organizar interior da geladeira']
          }
        }
      end

      it { expect(described_class.call(data)).to be_nil }
    end

    context 'when request failure' do
      before { allow(Request).to receive(:call).and_return(content) }

      let(:content) do
        {
          'error' => {
            'code' => 400,
            'message' => 'API key not valid. Please pass a valid API key.',
            'status' => 'INVALID_ARGUMENT'
          }
        }
      end

      let(:data) do
        {
          feedback: {
            id: 'feedback_id',
            tasker_id: 'tasker_id',
            review: 'Mudou muitas coisas de lugar na casa',
            service: 'cleaning'
          },
          job: {
            optionals: ['Limpar interior das janelas', 'Limpar e organizar interior da geladeira']
          }
        }
      end

      it { expect(described_class.call(data)).to eq(content) }
      it { expect(described_class.call(data)).not_to include(content[:processed_feedback]) }
    end
  end
end
