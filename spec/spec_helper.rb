# frozen_string_literal: true

if ENV.fetch('COVERAGE', 'false') == 'true'
  require 'simplecov'
  require 'simplecov_json_formatter'

  SimpleCov.formatters = SimpleCov::Formatter::MultiFormatter.new([SimpleCov::Formatter::HTMLFormatter,
                                                                   SimpleCov::Formatter::JSONFormatter])

  SimpleCov.start do
    enable_coverage :branch
  end
end

require 'rspec'
require 'functions_framework/testing'

RSpec.configure do |config|
  config.include FunctionsFramework::Testing, type: :function
  config.order = :random
  config.profile_examples = 3
end
