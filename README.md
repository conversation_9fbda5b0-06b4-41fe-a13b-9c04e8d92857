# Descrição

Serviço responsável por receber feedbacks dos clientes, processar através da API do Gemini e publicar os feedbacks processados em uma fila no Pubsub.

## Para configurar a aplicação
```
docker build . -t gemini
```

## Para rodar a aplicação

```
docker run --rm -e GOOGLE_APPLICATION_CREDENTIALS="/app/.config/gcloud/application_default_credentials.json" --mount type=bind,source=${HOME}/.config/gcloud,target=/app/.config/gcloud -e GEMINI_API_KEY=$(gcloud --project parafuzo-qa-infra secrets versions access latest --secret=gemini-api-key) -v ${PWD}/:/app -it gemini
```

## Testes

```
rspec
```

## Linter

```
rubocop
```
