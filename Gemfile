# frozen_string_literal: true

source 'https://rubygems.org'

ruby '~> 3.2'

# Google Cloud Functions
gem 'functions_framework', '~> 1.6'

# Google Cloud PubSub
gem 'google-cloud-pubsub', '~> 2.22'

# Dependencies with specified platform required
platform :ruby do
  gem 'google-protobuf', '~> 4.31'
  gem 'grpc', '~> 1.72'
end

group :development, :test do
  gem 'debug', '~> 1.10'
end

group :development do
  # Source quality
  gem 'rubocop', '~> 1.76'
  gem 'rubocop-performance', '~> 1.25'
  gem 'rubocop-rspec', '~> 3.6'
end

group :test do
  # Test framework
  gem 'rspec', '~> 3.13'

  # Code coverage for Ruby
  gem 'simplecov', '~> 0.22', require: false
  gem 'simplecov_json_formatter', '~> 0.1.4'
end
