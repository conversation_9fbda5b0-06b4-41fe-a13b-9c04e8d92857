name: quality

on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - master

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  lint-and-tests:
    uses: parafuzo/actions/.github/workflows/ruby-quality.yml@master
    with:
      ruby-version: "3.2"
      project-key: "parafuzo_service-gemini"
    secrets: inherit

  auto-merge:
    uses: parafuzo/actions/.github/workflows/auto-merge.yml@master
    needs: [lint-and-tests]
