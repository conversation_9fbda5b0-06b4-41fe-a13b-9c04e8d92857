name: deploy

on:
  workflow_dispatch:
  release:
    types: [published]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  deploy:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    env:
      PROJECT_ID: ${{ vars.PROJECT_ID }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - id: deploy
        run: |
          gcloud functions deploy process_feedback \
            --no-gen2 \
            --runtime ruby32 \
            --region us-east1 \
            --memory 128MiB \
            --verbosity error \
            --max-instances 3 \
            --project "${{ env.PROJECT_ID }}" \
            --set-secrets "GEMINI_API_KEY=gemini-api-key:latest" \
            --trigger-topic gemini-do-process-feedback
